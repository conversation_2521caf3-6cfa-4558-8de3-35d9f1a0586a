using System.Linq.Expressions;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Numerics;

namespace EAMS.Infrastructure.Repositories;
public abstract class  Repository<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : BaseEntity<TKey>
    where TKey : struct
{
    private readonly EamsDbContext _context;
    private readonly DbSet<TEntity> _dbSet;

    public Repository(EamsDbContext context)
    {
        _context = context;
        _dbSet = _context.Set<TEntity>();
    }

    public virtual async Task<IEnumerable<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> where = null, params Expression<Func<TEntity, object>>[] includes)
    {
        IQueryable<TEntity> query = _dbSet;

        // Apply includes for eager loading of navigation properties
        if (includes != null)
        {
            query = includes.Aggregate(query, (current, include) => current.Include(include));
        }

        // Apply the where clause if provided
        if (where != null)
        {
            query = query.Where(where);
        }

        return await query.ToListAsync();
    }

    public virtual async Task<TEntity> GetByIdAsync(TKey id, params Expression<Func<TEntity, object>>[] includes)
    {
        IQueryable<TEntity> query = _dbSet.Where(e => e.Id.Equals(id));

        // Apply includes
        if (includes != null)
        {
            query = includes.Aggregate(query, (current, include) => current.Include(include));
        }

        return await query.FirstOrDefaultAsync();
    }

    public virtual async Task AddAsync(TEntity entity)
    {
        await _dbSet.AddAsync(entity);
        await _context.SaveChangesAsync();
    }

    public virtual async Task UpdateAsync(TEntity entity)
    {
        _dbSet.Update(entity);
        await _context.SaveChangesAsync();
    }

    public virtual async Task DeleteAsync(TKey id)
    {
        var entity = await _dbSet.FindAsync(id);
        if (entity != null)
        {
            if (entity is SoftDeletableEntity<TKey> softDeleteEntity)
            {
                softDeleteEntity.DiscardedAt = DateTime.Now;
                _dbSet.Update(entity);
            }
            else
            {
                _dbSet.Remove(entity);
            }
            await _context.SaveChangesAsync();
        }
    }
}