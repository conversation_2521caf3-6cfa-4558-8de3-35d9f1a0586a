﻿using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Identity.Web;

namespace EAMS.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly IGraphUserService _userService;
        private readonly IMapper _mapper;
        public UsersController(IGraphUserService userService, IMapper mapper)
        {
            _userService = userService;
            _mapper = mapper;
        }

        [HttpGet("profile")]
        public async Task<ActionResult<UserDto>> GetUserProfile()
        {
            try
            {
                var me = await _userService.GetCurrentLoginUserAsync();
                if (me == null)
                {
                    return NotFound("User profile not found");
                }

                return Ok(_mapper.Map<UserDto>(me));
            }
                return StatusCode(ex.ResponseStatusCode ?? 500, ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
            }
        }

        [Authorize(Roles="Managers")]
        [HttpPost("invite")]
        public IActionResult InviteUser(Invitation invitation)
        {
            HttpContext httpContext = this.HttpContext;
            var currentUser = httpContext.User;

            return Ok();
        }
    }
}
