﻿using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using GraphUser = Microsoft.Graph.Models.User;

namespace EAMS.API.Mappings
{
    public class GraphMapperProfile: Profile
    {
        public GraphMapperProfile() {
            CreateMap<GraphUser, UserDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src  => src.Id != null ? Guid.Parse(src.Id) : Guid.Empty))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Mail));

            CreateMap<InvitationDto, UserInvitation>();
        }
    }
}
