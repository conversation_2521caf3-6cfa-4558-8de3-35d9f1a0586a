using System.ComponentModel.DataAnnotations.Schema;

namespace EAMS.Domain.Entities;

public class UserInvitation
{
    public Guid InvitationId { get; set; } = Guid.Empty;
    public Guid InvitedUserId { get; set; }
    public Guid InvitedByUserId { get; set; }
    public Guid TargetOrganisationId { get; set; }
    public string EntraResponse { get; set; }
    public string InviteRedirectUrl { get; set; }
    [NotMapped]
    public string InvitedUserEmailAddress { get; set; }
    
    // Navigation properties for relationships
    public User InvitedUser { get; set; }
    public User InvitedByUser { get; set; }
    public Organisation TargetOrganisation { get; set; }
}